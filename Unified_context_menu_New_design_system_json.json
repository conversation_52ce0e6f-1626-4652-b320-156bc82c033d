{"flutter_design_system_profile": {"name": "Flutter Dictionary App - Material Design 3", "version": "1.0", "flutter_version": "3.24+", "material_version": "Material Design 3 (Material You)", "design_philosophy": "Clean, adaptive Flutter interface following Material 3 principles with dynamic theming support", "theme_configuration": {"color_scheme": {"seed_color": "#4285F4", "brightness": "light", "primary": "#4285F4", "onPrimary": "#FFFFFF", "primaryContainer": "#E3F2FD", "onPrimaryContainer": "#0D47A1", "secondary": "#5F6368", "onSecondary": "#FFFFFF", "surface": "#FFFFFF", "onSurface": "#1C1B1F", "surfaceVariant": "#F8F9FA", "onSurfaceVariant": "#49454F", "outline": "#79747E", "shadow": "#000000", "inverseSurface": "#313033", "onInverseSurface": "#F4EFF4"}, "typography": {"text_theme": "Material 3 default with custom scaling", "display_large": {"font_family": "Roboto", "font_size": "32.0", "font_weight": "FontWeight.w700", "height": "1.12", "letter_spacing": "-0.5"}, "headline_medium": {"font_family": "Roboto", "font_size": "20.0", "font_weight": "FontWeight.w500", "height": "1.3", "letter_spacing": "0.0"}, "body_large": {"font_family": "Roboto", "font_size": "16.0", "font_weight": "FontWeight.w400", "height": "1.5", "letter_spacing": "0.5"}, "body_medium": {"font_family": "Roboto", "font_size": "14.0", "font_weight": "FontWeight.w400", "height": "1.43", "letter_spacing": "0.25"}}}, "layout_structure": {"scaffold": {"app_bar": "custom_app_bar", "body": "single_child_scroll_view", "safe_area": "true", "resize_to_avoid_bottom_inset": "true"}, "spacing": {"padding": "EdgeInsets.symmetric(horizontal: 16.0, vertical: 12.0)", "margin": "EdgeInsets.all(8.0)", "gap": "SizedBox(height: 16.0)"}}, "flutter_widgets": {"context_menu_overlay": {"widget": "Overlay", "positioning": "Positioned", "constraints": {"max_width": "MediaQuery.of(context).size.width - 32.0", "max_height": "MediaQuery.of(context).size.height * 0.4"}, "auto_positioning": {"horizontal": "Clamp between 16.0 and screen_width - card_width - 16.0", "vertical": "Above selection if space, below if not, center if neither"}}, "floating_dictionary_card": {"widget": "Material", "elevation": "8.0", "shape": "RoundedRectangleBorder(borderRadius: BorderRadius.circular(12.0))", "clip_behavior": "Clip.anti<PERSON>lias", "child": "Container", "constraints": "BoxConstraints(maxWidth: MediaQuery.of(context).size.width - 32.0, maxHeight: MediaQuery.of(context).size.height * 0.4)", "padding": "EdgeInsets.all(16.0)", "structure": [{"widget": "SingleChildScrollView", "child": "Column", "cross_axis_alignment": "CrossAxisAlignment.start", "main_axis_size": "MainAxisSize.min", "children": [{"widget": "Row", "main_axis_alignment": "MainAxisAlignment.spaceBetween", "children": [{"widget": "Expanded", "child": "Text", "style": "Theme.of(context).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold)", "data": "selected_word", "overflow": "TextOverflow.ellipsis"}, {"widget": "IconButton", "icon": "Icons.close", "constraints": "BoxConstraints(minWidth: 32, minHeight: 32)", "padding": "EdgeInsets.all(4.0)"}]}, {"widget": "Text", "style": "TextStyle(color: Theme.of(context).colorScheme.primary, fontStyle: FontStyle.italic)", "data": "pronunciation"}, {"widget": "SizedBox", "height": "12.0"}, {"widget": "Text", "style": "Theme.of(context).textTheme.bodyMedium", "data": "definition", "max_lines": "4", "overflow": "TextOverflow.ellipsis"}, {"widget": "SizedBox", "height": "12.0"}, {"widget": "Row", "main_axis_alignment": "MainAxisAlignment.spaceEvenly", "children": [{"widget": "TextButton", "style": "TextButton.styleFrom(padding: EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0))", "child": "Text('Translate')"}, {"widget": "TextButton", "style": "TextButton.styleFrom(padding: EdgeInsets.symmetric(horizontal: 12.0, vertical: 8.0))", "child": "Text('More')"}]}]}]}, "navigation_bar": {"widget": "Row", "main_axis_alignment": "MainAxisAlignment.spaceBetween", "children": [{"type": "Row", "children": ["IconButton", "IconButton", "IconButton"]}, {"type": "Row", "children": ["TextButton", "TextButton", "TextButton"]}, {"type": "Row", "children": ["IconButton"]}], "styling": {"height": "56.0", "padding": "EdgeInsets.symmetric(horizontal: 16.0)"}}, "primary_button": {"widget": "FilledButton", "style": "FilledButton.styleFrom(padding: EdgeInsets.symmetric(horizontal: 24.0, vertical: 12.0), shape: RoundedRectangleBorder(borderRadius: BorderRadius.circular(24.0)))", "child": "Text"}, "tab_buttons": {"widget": "Row", "main_axis_alignment": "MainAxisAlignment.spaceEvenly", "children": [{"widget": "TextButton", "style": "TextButton.styleFrom(foregroundColor: active ? Theme.of(context).colorScheme.primary : Theme.of(context).colorScheme.onSurfaceVariant)"}]}, "utility_buttons": {"widget": "IconButton", "constraints": "BoxConstraints(minWidth: 44.0, minHeight: 44.0)", "icon": "Icon", "icon_size": "24.0", "color": "Theme.of(context).colorScheme.onSurface"}, "language_selector": {"widget": "Row", "children": [{"widget": "Icon", "size": "20.0"}, {"widget": "SizedBox", "width": "8.0"}, {"widget": "Text", "style": "Theme.of(context).textTheme.bodyMedium"}, {"widget": "Icon", "icon": "Icons.keyboard_arrow_down"}]}}, "material_3_tokens": {"spacing": {"xs": "4.0", "sm": "8.0", "md": "16.0", "lg": "24.0", "xl": "32.0", "xxl": "48.0"}, "border_radius": {"none": "0.0", "xs": "4.0", "sm": "8.0", "md": "12.0", "lg": "16.0", "xl": "24.0", "full": "999.0"}, "elevation": {"level0": "0.0", "level1": "1.0", "level2": "3.0", "level3": "6.0", "level4": "8.0", "level5": "12.0"}}, "component_specifications": {"app_bar": {"widget": "AppBar", "elevation": "0.0", "background_color": "Theme.of(context).colorScheme.surface", "foreground_color": "Theme.of(context).colorScheme.onSurface", "title": "null", "leading": "IconButton", "actions": "List<Widget>"}, "icon_specifications": {"source": "Icons (Material Icons)", "size": "24.0", "color": "Theme.of(context).colorScheme.onSurface", "examples": ["Icons.format_underlined", "Icons.edit", "Icons.face", "Icons.content_copy", "Icons.chat_bubble_outline", "Icons.keyboard_arrow_down"]}, "text_styling": {"main_word": {"style": "Theme.of(context).textTheme.displayLarge?.copyWith(fontWeight: FontWeight.bold)", "color": "Theme.of(context).colorScheme.onSurface"}, "pronunciation": {"style": "TextStyle(fontSize: 18, fontStyle: FontStyle.italic)", "color": "Theme.of(context).colorScheme.primary"}, "definition": {"style": "Theme.of(context).textTheme.bodyLarge", "color": "Theme.of(context).colorScheme.onSurface"}, "tab_active": {"style": "Theme.of(context).textTheme.labelLarge?.copyWith(fontWeight: FontWeight.w500)", "color": "Theme.of(context).colorScheme.primary"}, "tab_inactive": {"style": "Theme.of(context).textTheme.labelLarge", "color": "Theme.of(context).colorScheme.onSurfaceVariant"}}}, "responsive_breakpoints": {"mobile": "< 600", "tablet": "600 - 1200", "desktop": "> 1200", "adaptive_widgets": {"use": "LayoutBuilder", "breakpoint_helper": "MediaQuery.of(context).size.width"}}, "animations": {"duration": "Duration(milliseconds: 200)", "curve": "Curves.easeInOut", "page_transitions": "PageTransitionsTheme", "hero_animations": "Hero widget for shared elements", "implicit_animations": ["AnimatedContainer", "AnimatedOpacity", "AnimatedPadding"]}, "state_management": {"recommended": ["Provider", "Riverpod", "Bloc"], "theme_switching": "ThemeMode.system with dynamic color support", "localization": "flutter_localizations with intl package"}, "accessibility": {"semantics": {"labels": "Semantics(label: 'descriptive_text')", "buttons": "Semantics(button: true)", "headers": "Semantics(header: true)"}, "focus": {"traversal": "FocusTraversalGroup", "auto_focus": "autofocus: true on primary actions"}, "contrast": "Follows Material 3 contrast ratios automatically"}, "implementation_patterns": {"overlay_positioning": {"pattern": "OverlayEntry with custom positioning logic", "horizontal_clamping": "math.max(16.0, math.min(tapPosition.dx, screenWidth - cardWidth - 16.0))", "vertical_positioning": "Calculate based on available space above/below selection", "boundary_detection": "Check MediaQuery.of(context).size and SafeArea.of(context)"}, "gesture_detection": {"text_selection": "SelectableText with onSelectionChanged", "tap_outside_dismiss": "GestureDetector with HitTestBehavior.translucent", "selection_rect": "Get TextSelection.baseOffset and TextSelection.extentOffset positions"}, "responsive_constraints": {"max_width": "MediaQuery.of(context).size.width - 32.0", "max_height": "MediaQuery.of(context).size.height * 0.4", "min_padding": "EdgeInsets.all(16.0) from screen edges"}, "theme_usage": "Theme.of(context).colorScheme.primary", "responsive_padding": "EdgeInsets.symmetric(horizontal: 16.0).copyWith(top: MediaQuery.of(context).padding.top)", "safe_area": "<PERSON><PERSON><PERSON>(child: <PERSON><PERSON><PERSON><PERSON>())", "ink_well_pattern": "InkWell(onTap: () {}, borderRadius: BorderRadius.circular(8), child: Padding(...))", "custom_scroll_behavior": "ScrollConfiguration.of(context).copyWith(scrollbars: false)"}, "packages_dependencies": {"required": ["flutter/material.dart", "flutter/services.dart"], "recommended": ["flutter_localizations", "dynamic_color", "shared_preferences", "provider or riverpod"]}, "code_generation_hints": {"overlay_implementation": "Use OverlayEntry with Positioned widget for floating context menu", "positioning_logic": "Calculate x,y coordinates based on text selection and screen boundaries", "constraints_enforcement": "Always wrap in ConstrainedBox with maxWidth and maxHeight", "dismissal_pattern": "Stack with GestureDetector covering full screen for tap-outside-to-dismiss", "text_overflow": "Use maxLines and TextOverflow.ellipsis to prevent vertical expansion", "compact_layout": "SingleChildScrollView with MainAxisSize.min for content-based sizing", "widget_structure": "StatefulWidget for interactive components, StatelessWidget for static display", "build_method": "Always return single widget tree with proper Material 3 theming", "performance": "Use const constructors where possible, ListView.builder for lists", "testing": "Include Key parameters for widget testing"}}}